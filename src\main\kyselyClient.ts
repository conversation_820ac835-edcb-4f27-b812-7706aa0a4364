// Kysely database client for better-sqlite3
import Database from 'better-sqlite3'
import { Kysely, SqliteDialect, sql } from 'kysely'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'
import type { Database as DatabaseSchema } from '../shared/database-types'
import { configManager } from './configManager'

let db: Kysely<DatabaseSchema> | null = null

// 全局变量存储自定义工作目录
let customWorkspaceDirectory: string | null = null

// 设置自定义工作目录
export function setWorkspaceDirectory(directory: string): void {
  console.log('=== [kyselyClient] setWorkspaceDirectory CALLED ===')
  console.log('[kyselyClient] Previous customWorkspaceDirectory:', customWorkspaceDirectory)
  console.log('[kyselyClient] New directory:', directory)
  customWorkspaceDirectory = directory
  console.log('[kyselyClient] ✅ Custom workspace directory set in kyselyClient:', directory)
  console.log('=== [kyselyClient] setWorkspaceDirectory END ===')
}

// 从localStorage读取用户设置
function loadUserSettingsFromStorage(): string | null {
  try {
    const userDataPath = app.getPath('userData')
    console.log('=== [kyselyClient] loadUserSettingsFromStorage START ===')
    console.log('[kyselyClient] Searching for user settings in:', userDataPath)

    // Electron的localStorage通常存储在这些位置
    const possiblePaths = [
      path.join(userDataPath, 'Local Storage', 'leveldb'),
      path.join(userDataPath, 'IndexedDB'),
      path.join(userDataPath, 'Session Storage')
    ]

    for (const storagePath of possiblePaths) {
      if (fs.existsSync(storagePath)) {
        console.log('[kyselyClient] Found storage directory:', storagePath)
        try {
          const files = fs.readdirSync(storagePath)

          for (const file of files) {
            const filePath = path.join(storagePath, file)
            try {
              const content = fs.readFileSync(filePath, 'utf8')
              console.log(`[kyselyClient] Checking file: ${file}, size: ${content.length}`)

              // 检查是否包含用户设置相关数据
              // Zustand persist使用键名 'user-settings'
              const hasUserSettings = content.includes('user-settings') ||
                                     content.includes('workspaceDirectory') ||
                                     content.includes('"settings"')

              if (hasUserSettings) {
                console.log('[kyselyClient] Found potential user-settings in file:', file)
                console.log('[kyselyClient] Content preview:', content.substring(0, 500) + '...')

                // 尝试多种模式匹配workspaceDirectory - 支持Zustand persist格式
                const patterns = [
                  // 标准JSON格式
                  /"workspaceDirectory":"([^"]+)"/,
                  /'workspaceDirectory':'([^']+)'/,
                  // URL编码格式
                  /workspaceDirectory%22%3A%22([^%]+)%22/,
                  // Zustand persist格式 - 嵌套在state.settings中
                  /"state"[^}]*"settings"[^}]*"workspaceDirectory":"([^"]+)"/,
                  // 直接在settings中
                  /"settings"[^}]*"workspaceDirectory":"([^"]+)"/,
                  // 处理可能的转义
                  /workspaceDirectory[^"]*"([^"]+)"/,
                  /workspaceDirectory[^']*'([^']+)'/,
                  // 处理路径格式
                  /workspaceDirectory.*?([A-Z]:\\[^"'\s,}]+)/i,
                  /workspaceDirectory.*?(\/[^"'\s,}]+)/,
                  // 更宽松的匹配
                  /workspaceDirectory['":\s]+([^'",\s}]+)/
                ]

                for (const pattern of patterns) {
                  const match = content.match(pattern)
                  if (match && match[1]) {
                    let workspaceDir = match[1]

                    // 处理URL解码
                    try {
                      workspaceDir = decodeURIComponent(workspaceDir)
                    } catch (e) {
                      // 如果解码失败，使用原始值
                    }

                    // 处理JSON转义
                    workspaceDir = workspaceDir.replace(/\\\\/g, '\\')
                    workspaceDir = workspaceDir.replace(/\\"/g, '"')
                    workspaceDir = workspaceDir.replace(/\\'/g, "'")

                    console.log('[kyselyClient] ✅ Extracted workspaceDirectory:', workspaceDir)
                    console.log('=== [kyselyClient] loadUserSettingsFromStorage SUCCESS ===')
                    return workspaceDir
                  }
                }

                // 尝试直接解析JSON
                try {
                  console.log('[kyselyClient] Attempting to parse content as JSON...')
                  const jsonData = JSON.parse(content)

                  // 检查各种可能的JSON结构
                  const possiblePaths = [
                    jsonData?.state?.settings?.workspaceDirectory,
                    jsonData?.settings?.workspaceDirectory,
                    jsonData?.workspaceDirectory
                  ]

                  for (const path of possiblePaths) {
                    if (path && typeof path === 'string' && path.length > 3) {
                      console.log('[kyselyClient] ✅ Found workspaceDirectory in JSON:', path)
                      console.log('=== [kyselyClient] loadUserSettingsFromStorage SUCCESS ===')
                      return path
                    }
                  }
                } catch (e) {
                  console.log('[kyselyClient] Content is not valid JSON, continuing with regex patterns')
                }

                console.log('[kyselyClient] Found user-settings but no workspaceDirectory')
              }

            } catch (e) {
              continue
            }
          }
        } catch (error) {
          console.warn('Failed to read storage directory:', storagePath, error)
        }
      }
    }

    console.log('[kyselyClient] ❌ No user settings found in localStorage')
    console.log('=== [kyselyClient] loadUserSettingsFromStorage FAILED ===')
    return null
  } catch (error) {
    console.warn('[kyselyClient] Failed to load user settings from storage:', error)
    console.log('=== [kyselyClient] loadUserSettingsFromStorage ERROR ===')
    return null
  }
}

// 获取用户设置的工作目录
function getUserWorkspaceDirectory(): string {
  console.log('=== [kyselyClient] getUserWorkspaceDirectory START ===')
  console.log('[kyselyClient] customWorkspaceDirectory:', customWorkspaceDirectory)

  if (customWorkspaceDirectory) {
    console.log('[kyselyClient] ✅ Using custom workspace directory for database:', customWorkspaceDirectory)
    console.log('=== [kyselyClient] getUserWorkspaceDirectory END (custom) ===')
    return customWorkspaceDirectory
  }

  // 使用配置管理器读取
  console.log('[kyselyClient] Attempting to load from config manager...')
  const savedWorkspaceDir = configManager.getWorkspaceDirectory()
  if (savedWorkspaceDir) {
    console.log('[kyselyClient] ✅ Using saved workspace directory for database:', savedWorkspaceDir)
    console.log('=== [kyselyClient] getUserWorkspaceDirectory END (saved) ===')
    return savedWorkspaceDir
  }

  const defaultPath = app.getPath('userData')
  console.log('[kyselyClient] ⚠️ Using default userData directory for database:', defaultPath)
  console.log('=== [kyselyClient] getUserWorkspaceDirectory END (default) ===')
  return defaultPath
}

export function createKyselyClient(): Kysely<DatabaseSchema> {
  if (db) {
    return db
  }

  const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged

  let databasePath: string

  console.log('=== [kyselyClient] createKyselyClient DATABASE PATH SETUP ===')
  console.log('[kyselyClient] isDevelopment:', isDevelopment)

  // 始终使用用户工作目录，无论开发还是生产环境
  const userWorkspaceDir = getUserWorkspaceDirectory()
  console.log('[kyselyClient] Final userWorkspaceDir:', userWorkspaceDir)

  // 确保paolife_data目录存在
  const dataDir = path.join(userWorkspaceDir, 'paolife_data')
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true })
    console.log('[kyselyClient] Created paolife_data directory:', dataDir)
  }

  if (isDevelopment) {
    // 开发环境：使用用户工作目录下的paolife_data/dev.db
    databasePath = path.join(dataDir, 'dev.db')
    console.log('[kyselyClient] 🔧 Development database path (user workspace):', databasePath)
  } else {
    // 生产环境：使用用户工作目录下的paolife_data/paolife.db
    databasePath = path.join(dataDir, 'paolife.db')
    console.log('[kyselyClient] 🚀 Production database path (user workspace):', databasePath)
  }

  console.log('=== [kyselyClient] FINAL DATABASE PATH:', databasePath, '===')

  // Ensure directory exists
  const dbDir = path.dirname(databasePath)
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true })
  }

  // Create better-sqlite3 instance
  const sqlite = new Database(databasePath)

  // Enable foreign keys
  sqlite.pragma('foreign_keys = ON')

  // Enable WAL mode for better performance
  sqlite.pragma('journal_mode = WAL')

  // Create Kysely instance
  db = new Kysely<DatabaseSchema>({
    dialect: new SqliteDialect({
      database: sqlite
    })
  })

  console.log(`Kysely database client created successfully at: ${databasePath}`)
  console.log(`Database mode: ${isDevelopment ? 'Development' : 'Production'}`)

  return db
}

export function getKyselyClient(): Kysely<DatabaseSchema> {
  if (!db) {
    throw new Error('Database not initialized. Call createKyselyClient() first.')
  }
  return db
}

export async function closeKyselyClient(): Promise<void> {
  if (db) {
    await db.destroy()
    db = null
    console.log('Kysely database client closed')
  }
}

// Helper function to handle JSON serialization
export function serializeJson(value: any): string {
  return JSON.stringify(value)
}

// Helper function to handle JSON deserialization
export function deserializeJson<T = any>(value: string | null): T | null {
  if (value === null) return null
  try {
    return JSON.parse(value)
  } catch (error) {
    console.warn('Failed to parse JSON:', value, error)
    return null
  }
}

// Helper function to convert boolean to SQLite integer
export function booleanToInt(value: boolean | undefined | null): number {
  return value ? 1 : 0
}

// Helper function to convert SQLite integer to boolean
export function intToBoolean(value: number): boolean {
  return value === 1
}

// Helper function to convert Date to ISO string
export function dateToString(date: Date): string {
  return date.toISOString()
}

// Helper function to convert ISO string to Date
export function stringToDate(dateString: string): Date {
  return new Date(dateString)
}

// Helper function to get current timestamp
export function getCurrentTimestamp(): string {
  return new Date().toISOString()
}

// Helper function to generate CUID-like ID
export function generateId(): string {
  const timestamp = Date.now().toString(36)
  const randomPart = Math.random().toString(36).substring(2, 15)
  return `c${timestamp}${randomPart}`
}

// Transaction helper
export async function withTransaction<T>(
  db: Kysely<DatabaseSchema>,
  callback: (trx: Kysely<DatabaseSchema>) => Promise<T>
): Promise<T> {
  return await db.transaction().execute(callback)
}

// Error handling helper
export function handleDatabaseError(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  return 'Unknown database error'
}

// Migration helper
export async function runMigrations(): Promise<void> {
  const client = getKyselyClient()

  // Read migration SQL file
  // Try multiple possible locations for the SQL file
  const possiblePaths = [
    path.join(__dirname, 'migrations.sql'),
    path.join(process.cwd(), 'src', 'main', 'migrations.sql'),
    path.join(app.getAppPath(), 'src', 'main', 'migrations.sql'),
    path.join(process.resourcesPath, 'app', 'src', 'main', 'migrations.sql')
  ]

  let migrationSql: string
  let foundPath: string | null = null

  for (const migrationPath of possiblePaths) {
    try {
      if (fs.existsSync(migrationPath)) {
        migrationSql = fs.readFileSync(migrationPath, 'utf-8')
        foundPath = migrationPath
        console.log(`Found migrations.sql at: ${migrationPath}`)
        break
      }
    } catch (error) {
      // Continue to next path
    }
  }

  if (!foundPath) {
    throw new Error(`Could not find migrations.sql file. Tried paths: ${possiblePaths.join(', ')}`)
  }

  // Split by semicolon and execute each statement
  // First, remove comments and empty lines
  const cleanedSql = migrationSql!
    .split('\n')
    .filter(line => {
      const trimmed = line.trim()
      return trimmed.length > 0 && !trimmed.startsWith('--')
    })
    .join('\n')

  // Split by semicolon and clean up statements
  const statements = cleanedSql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0)

  console.log(`Running ${statements.length} migration statements...`)

  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i]
    try {
      console.log(`Executing statement ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`)
      // Use sql template literal for raw SQL execution
      await sql`${sql.raw(statement)}`.execute(client)
      console.log('✓ Migration statement executed successfully')
    } catch (error) {
      console.error('✗ Migration statement failed:', statement.substring(0, 100) + '...')
      console.error('Full statement:', statement)
      console.error('Error:', error)
      throw error
    }
  }

  console.log('All migrations completed successfully')
}
