# 常用模式和最佳实践

- MarkdownEditor 组件实现了真正的单实例模式，解决了内容更新重复问题。关键技术：1) 移除 key 属性避免组件重建；2) 使用 isUpdatingRef 标记防止 replaceAll 和 markdownUpdated 循环调用；3) 优化 useEffect 依赖避免重复初始化。markdownUpdated 用于用户编辑，replaceAll 用于程序化更新，通过标记机制避免循环。
- 已完成 WikiLink 双向链接插件开发。插件支持 [[页面名称]] 和 [[页面名称|显示文本]] 语法，包含自动补全、悬停预览、双向链接功能。关键技术：基于 Milkdown 插件系统，使用 ProseMirror 节点定义，集成文件系统 API。修复了 markdownUpdated 循环更新问题，使用 isUpdatingRef 标记防止程序化更新触发回调循环。
- WikiLink 插件集成方案：采用最小化集成方式，在 MarkdownEditor.tsx 的 useEditor 钩子中通过 crepe.editor.use() 注入插件，只启用输入规则功能，配置页面点击和链接创建回调，避免与 Crepe 内置功能冲突
- WikiLink 渲染持久性问题根因：Remark 插件被禁用导致 Markdown 序列化/反序列化链路断裂。输入规则只负责输入转换，但缺少 Markdown 解析阶段的 [[内容]] 文本到 WikiLink 节点的转换。解决方案：启用 wikiLinkRemarkPlugin 提供完整的 Markdown 解析支持。
- WikiLink 嵌套编辑器架构实现：1) NestedPreviewManager 单例管理多层级预览窗口 2) PreviewInstance 类管理独立的 Milkdown 编辑器实例 3) 支持最大5层嵌套，每层有独立的z-index和位置偏移 4) 完整的生命周期管理：创建时异步加载内容和编辑器，销毁时清理所有资源 5) createNestedPreviewPlugin 为每个预览编辑器注入嵌套预览功能 6) 智能定位避免超出视窗边界，层级颜色标识便于调试
- 实现了右侧滑出式任务详情面板：TaskDetailPanel 组件支持完整的任务编辑功能，包括基本信息、状态管理、时间管理、进度跟踪、项目关联等。使用 CSS Transform 实现平滑滑动动画，支持点击外部区域和 ESC 键关闭。集成到 ProjectDetailPage 中，点击任务卡片即可打开详情面板进行编辑。
- PaoLife 任务管理功能优化完成：1) 实现无限层级子任务系统，支持任意深度嵌套，智能展开折叠，层级连接线显示，性能优化的虚拟化渲染；2) 实现右侧滑出式任务详情面板，完整编辑功能，平滑动画，响应式设计；3) 性能优化包括 memo 组件、虚拟化列表、响应式缩进；4) 提供测试数据生成器验证大量嵌套任务性能。
- 已完成领域管理页面和项目管理页面的确认弹窗样式统一化：将系统默认的confirm()弹窗替换为自定义ConfirmDialog组件，实现与项目详情页任务删除弹窗一致的UI风格。修改涉及ProjectsPage.tsx、AreasPage.tsx、AreaDetailPage.tsx、ArchiveItem.tsx四个文件，使用useConfirmDialog hook提供统一的弹窗交互体验。
- 领域KPI功能优化第一阶段完成：创建AreaMetricAdapter适配器复用项目模块的KPIDashboard、KPIChart、KPITrends组件；实现HabitTracker组件支持每日打卡、连续天数追踪、完成率统计；扩展AreaKPIManagement为5个标签页，智能分类显示习惯和常规指标。用户选择继续第二阶段：数据库扩展和高级功能。
- 领域KPI功能优化三阶段全部完成：第一阶段复用项目模块组件实现Dashboard、Charts、Trends功能；第二阶段扩展数据库schema支持习惯追踪和领域标准，创建增强版对话框；第三阶段实现高级分析组件AreaAnalytics、性能缓存系统AreaMetricCache、领域标准监控AreaStandardMonitor。修复了Switch组件导入问题，用Checkbox替代。现在领域KPI拥有完整的6标签页功能和企业级分析能力。
- 领域KPI功能优化全面完成：实现了双向KPI系统（增长型/减少型），包括AreaMetric和ProjectKPI的完整支持；创建了通用kpiProgressCalculator计算器；为所有创建对话框添加了方向选择器；实现了批量方向更新工具；完成了数据库schema扩展和迁移脚本；解决了减肥等减少型指标的进度计算问题；用户现在拥有完全的KPI方向控制权。
- 完成了领域详情页Key Metrics模块的完整国际化适配：1) 在LanguageContext.tsx中添加了完整的中英文翻译键，包括kpiManagement、habitTracker、metricHistory、kpiDashboard四个主要模块；2) 修改了AreaKPIManagement.tsx、HabitTracker.tsx、AreaMetricHistory.tsx、KPIDashboard.tsx四个组件，将所有硬编码英文文本替换为t()函数调用；3) 保持了与项目现有国际化实现方式的一致性，使用点分层级结构的翻译键；4) 确保了Key Metrics模块能够根据当前语言设置正确显示中文或英文内容。
- 项目KPI新方案：基于领域KPI的优秀设计，为ProjectDetailPage.tsx实施增强的KPI管理方案，包括统计概览区域（4个统计卡片）、ProjectKPIQuickInput快速录入组件、BatchProjectKPIRecordDialog批量操作、UniversalKPIDialog统一对话框，保持右侧单列布局不变
- 项目KPI新方案实施完成：成功将领域KPI的优秀设计移植到ProjectDetailPage.tsx，包括ProjectKPIQuickInput快速录入组件、BatchProjectKPIRecordDialog批量操作、统计概览区域、完整国际化支持、视觉体验增强、响应式布局优化。现在项目详情页拥有企业级KPI管理功能，用户体验与领域KPI保持一致
- 修复ProjectDetailPage.tsx翻译键显示问题：将LanguageContext.tsx中components.projectDetailPage层级移动到pages.projects.detail，解决翻译键路径不匹配导致显示键名而非翻译文本的问题
- 收件箱处理功能增强方案：实现双模式处理（创建新的/关联现有），创建新项目/领域时自动提取标题和描述并预填充对话框供用户确认，关联现有实体时提供添加到描述或创建关联任务两种方式，确保收件箱内容合理融入PARA系统
- 完成了PaoLife项目回顾模块的完整国际化适配：1) 在LanguageContext.tsx中添加了完整的中英文翻译键，包括页面标题、回顾计划、状态、模板、洞察和最近回顾等所有模块；2) 修改了ReviewsPage.tsx，将所有硬编码英文文本替换为t()函数调用，支持参数插值；3) 保持了与项目现有国际化实现方式的一致性，使用点分层级结构的翻译键；4) 确保了回顾模块能够根据当前语言设置正确显示中文或英文内容。
- 修复了PaoLife项目资源引用系统的关键问题：UnifiedReferenceService缺少ResourceLink表反向查找功能。添加了getResourceReferences数据库API和getResourceLinkReferences方法，实现了真正的双向引用查找。现在资源模块能正确显示通过项目/领域详情页关联的资源引用关系。
- 进一步修复了PaoLife资源引用系统的路径匹配问题：ResourceLink表存储绝对路径，而UnifiedReferenceService接收虚拟路径。添加了智能路径转换和多格式匹配机制，确保能正确查找到通过项目/领域详情页关联的资源引用关系。
- 修复了PaoLife资源引用系统的路径判断逻辑错误：原来的代码错误地将虚拟路径"/18.md"识别为Unix绝对路径。改进了路径判断逻辑，正确区分Windows绝对路径、Unix绝对路径和虚拟路径，确保虚拟路径能正确转换为绝对路径进行数据库查询。
- 修复了PaoLife资源引用系统的路径分隔符不匹配问题：数据库中存储反斜杠路径，查询时使用正斜杠路径。添加了多种路径格式尝试机制，确保能匹配到正确的ResourceLink记录。
- 修复了PaoLife项目UnifiedReferenceService中getWikiLinkReferences方法的错误：bidirectionalLinkService.getDocumentLinks返回的是包含backlinks和outlinks的对象，而不是直接的数组。正确处理了返回数据结构。
- 修复了PaoLife项目WikiLink引用显示0的问题：文件加载时WikiLink被正确解析但没有保存到数据库，因为被标记为程序化更新。添加了文件加载完成后的延迟WikiLink更新机制，确保WikiLink能正确保存到DocumentLink表中。
- 修复了PaoLife项目WikiLink计算数量问题：1)重新启用缓存机制避免重复查询；2)优化延迟更新逻辑，从1秒改为100ms并只在检测到WikiLink时触发；3)添加防重复查询机制，避免同一文件被多次并发查询；4)增强缓存日志便于调试。
- 修复了PaoLife项目引用统计不更新问题：WikiLink更新后UnifiedReferenceService的缓存没有被清理，导致引用面板显示旧数据。在MarkdownEditor的updateBidirectionalLinks方法中添加了缓存清理逻辑，确保引用统计能及时更新。
- 实现了PaoLife项目的配置管理器解决方案：创建ConfigManager类处理用户配置的持久化存储，使用JSON配置文件替代localStorage依赖，支持从localStorage自动迁移，提供首次启动检查、工作目录管理等功能。修改了主进程启动逻辑、IPC处理器、数据库服务和前端组件，实现了可靠的配置存储和读取机制。
- 修复了AreasPage.tsx的布局问题，参考ProjectsPage.tsx的布局结构：使用h-screen flex flex-col overflow-hidden的全屏布局，添加flex-1 flex flex-col min-h-0确保内容区域正确填充，为Areas Grid/List添加h-[calc(100vh-280px)]固定高度和overflow-y-auto滚动容器，确保无领域时的EmptyState能正确显示。
- 修复了PaoLife项目的退出确认弹窗和最小化到托盘功能：1.重新布局ExitConfirmDialog，将托盘提示移到按钮上方；2.添加DialogDescription修复Radix UI警告；3.增强最小化到托盘逻辑，支持不同平台的窗口隐藏方式；4.添加详细调试日志帮助排查问题；5.使用setSkipTaskbar确保窗口正确隐藏到托盘。
- 修复了PaoLife项目最小化到托盘功能的关键问题：发现IPC处理器中有重复的窗口状态检查和复杂的隐藏逻辑，导致窗口状态混乱。简化IPC处理器逻辑，使其与托盘服务的hideWindow方法完全一致：只检查最小化状态并restore，设置skipTaskbar，然后直接hide()。避免了重复的状态检查和不必要的复杂性。
- 重新实现了PaoLife项目的退出确认功能：1.删除了有问题的旧实现；2.创建了简化的新实现，包括主进程close事件处理、IPC处理器app-exit-action、preload API exitAction、新的SimpleExitConfirmDialog组件；3.使用更简单直接的事件流：close-requested -> 显示弹窗 -> app-exit-action -> 执行动作；4.避免了复杂的状态检查和异步问题。
